import requests
import json
import re
from typing import Dict, Any, Optional
from app.core.config import Settings

settings = Settings()

class AIService:
    """用于与AI服务交互的工具类"""

    def __init__(self, api_url: str = "http://10.0.17.102:8502",
                 access_token: str = "sk-ad30e03bc6c4477c84897dae975dccb8"):
        self.api_url = api_url
        self.access_token = access_token
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}"
        }

    def translate_title_to_branch_name(self, title: str) -> str:
        """
        将需求标题翻译成适合Git分支的英文名称

        Args:
            title: 需求标题

        Returns:
            翻译后的分支名称
        """
        print(f"[AI服务] 开始处理需求标题: '{title}'")
        prompt = f"""帮我把该需求标题，转换成git分支的英文名称：
需求标题：{title}。
要求： 1、给出你推算出的最合适的那个； 2、只需要翻译好的英文分支名称，不需要"dev/"，"feature/"这部分分类部分； 2、以json纯文本字符串返回 {{"branch_name": "xxxxx"}}"""

        payload = {
            "model": "SiliconFlow.Pro/deepseek-ai/DeepSeek-V3",
            "stream": False,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        }

        try:
            print(f"[AI服务] 发送请求到 {self.api_url}/api/chat/completions")
            print(f"[AI服务] 请求内容: {json.dumps(payload, ensure_ascii=False)}")

            response = requests.post(f"{self.api_url}/api/chat/completions",
                                    headers=self.headers,
                                    json=payload)

            print(f"[AI服务] 收到响应，状态码: {response.status_code}")

            if response.status_code != 200:
                print(f"[AI服务] 请求失败，状态码: {response.status_code}, 响应内容: {response.text}")
                # 如果请求失败，返回一个基于标题的简单英文名称
                fallback = self._fallback_branch_name(title)
                print(f"[AI服务] 使用备用方法生成分支名: {fallback}")
                return fallback

            # 解析响应
            print(f"[AI服务] 开始解析响应")
            response_text = response.text
            print(f"[AI服务] 原始响应内容: {response_text}")

            response_data = response.json()
            print(f"[AI服务] 解析后的JSON: {json.dumps(response_data, ensure_ascii=False)}")

            # 从choices中获取消息内容
            choices = response_data.get("choices", [])
            if not choices:
                print(f"[AI服务] 响应中没有choices数组或为空")
                fallback = self._fallback_branch_name(title)
                print(f"[AI服务] 使用备用方法生成分支名: {fallback}")
                return fallback

            print(f"[AI服务] choices数组长度: {len(choices)}")
            first_choice = choices[0]
            message = first_choice.get("message", {})
            content = message.get("content", "")
            print(f"[AI服务] 消息内容: {content}")

            # 处理可能包含的markdown代码块标记
            original_content = content
            if "```json" in content:
                print(f"[AI服务] 检测到JSON代码块标记")
                content = re.sub(r"```json\n|\n```", "", content)
            elif "```" in content:
                print(f"[AI服务] 检测到普通代码块标记")
                content = re.sub(r"```\n|\n```", "", content)

            if original_content != content:
                print(f"[AI服务] 移除代码块标记后的内容: {content}")

            # 尝试解析JSON字符串
            json_match = re.search(r"\{.*\}", content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                print(f"[AI服务] 提取的JSON字符串: {json_str}")
                try:
                    data = json.loads(json_str)
                    print(f"[AI服务] 解析JSON成功: {json.dumps(data, ensure_ascii=False)}")
                    branch_name = data.get("branch_name")
                    if branch_name:
                        print(f"[AI服务] 成功获取分支名: {branch_name}")
                        return branch_name
                    else:
                        print(f"[AI服务] JSON中没有branch_name字段")
                        fallback = self._fallback_branch_name(title)
                        print(f"[AI服务] 使用备用方法生成分支名: {fallback}")
                        return fallback
                except json.JSONDecodeError as e:
                    print(f"[AI服务] JSON解析失败: {str(e)}")
                    fallback = self._fallback_branch_name(title)
                    print(f"[AI服务] 使用备用方法生成分支名: {fallback}")
                    return fallback
            else:
                print(f"[AI服务] 未找到JSON格式内容")
                fallback = self._fallback_branch_name(title)
                print(f"[AI服务] 使用备用方法生成分支名: {fallback}")
                return fallback

        except Exception as e:
            print(f"[AI服务] 调用AI服务失败: {str(e)}")
            fallback = self._fallback_branch_name(title)
            print(f"[AI服务] 使用备用方法生成分支名: {fallback}")
            return fallback

    def _fallback_branch_name(self, title: str) -> str:
        """
        当AI服务调用失败时，生成一个基于标题的简单英文名称

        Args:
            title: 需求标题

        Returns:
            生成的分支名称
        """
        print(f"[AI服务] 使用备用方法处理标题: '{title}'")

        # 常见中文词汇的英文映射
        cn_to_en = {
            "需求": "requirement",
            "功能": "feature",
            "开发": "develop",
            "实现": "implement",
            "修复": "fix",
            "漏洞": "bug",
            "问题": "issue",
            "优化": "optimize",
            "改进": "improve",
            "新增": "add",
            "删除": "delete",
            "更新": "update",
            "管理": "manage",
            "系统": "system",
            "模块": "module",
            "接口": "api",
            "页面": "page",
            "用户": "user",
            "数据": "data",
            "文件": "file",
            "配置": "config",
            "设置": "setting",
            "重构": "refactor"
        }

        # 尝试替换常见中文词汇
        result = title
        for cn, en in cn_to_en.items():
            result = result.replace(cn, en)

        # 移除非字母数字字符，将空格替换为连字符
        result = result.lower().replace(" ", "-").replace("，", "-").replace("。", "").replace("：", "-")

        # 移除所有非ASCII字符
        result = ''.join(c for c in result if ord(c) < 128)

        # 确保结果不为空，如果为空则使用默认值
        if not result or result.strip('-') == '':
            result = "requirement-branch"

        # 移除连续的连字符
        while "--" in result:
            result = result.replace("--", "-")

        # 移除开头和结尾的连字符
        result = result.strip("-")

        print(f"[AI服务] 备用方法生成的分支名: '{result}'")
        return result

# 创建全局实例，方便导入使用
ai_service = AIService()
