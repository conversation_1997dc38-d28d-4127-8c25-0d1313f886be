from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Dict, Any
from app.core.security import get_current_user
from app.models.user import User
from app.utils.ai_service import ai_service

router = APIRouter()

class TranslateTitleRequest(BaseModel):
    title: str

class TranslateTitleResponse(BaseModel):
    branch_name: str

@router.post("/ai/translate-title-to-branch", response_model=TranslateTitleResponse)
def translate_title_to_branch_name(
    request: TranslateTitleRequest,
    current_user: User = Depends(get_current_user)
):
    """
    将需求标题翻译成适合Git分支的英文名称
    
    Args:
        request: 包含需求标题的请求体
        current_user: 当前用户（需要管理员权限）
    
    Returns:
        包含翻译后分支名称的响应
    """
    # 检查用户权限（只有管理员可以调用AI服务）
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="只有管理员可以使用AI服务"
        )
    
    try:
        # 调用AI服务翻译标题
        branch_name = ai_service.translate_title_to_branch_name(request.title)
        
        return TranslateTitleResponse(branch_name=branch_name)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI服务调用失败: {str(e)}"
        )
