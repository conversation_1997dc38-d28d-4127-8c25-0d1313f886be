from datetime import timedelta
import logging
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON>wordBearer
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_password, create_access_token, ACCESS_TOKEN_EXPIRE_MINUTES, get_current_user, get_password_hash, refresh_access_token
from app.models.user import User as UserModel
from app.schemas.user import Token, UserLogin, User, PasswordChange

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auth.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

@router.post("/login", response_model=Token)
async def login(
    user_data: UserLogin,
    db: Session = Depends(get_db)
):
    logger.info(f"尝试登录用户: {user_data.username}")

    # 查询用户
    user = db.query(UserModel).filter(UserModel.username == user_data.username).first()

    # 记录用户查询结果
    if not user:
        logger.error(f"登录失败: 用户 {user_data.username} 不存在")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    logger.info(f"找到用户: {user.username}, ID: {user.id}, 管理员: {user.is_admin}")
    logger.info(f"数据库中的密码哈希: {user.password}")

    # 验证密码
    is_password_correct = verify_password(user_data.password, user.password)
    logger.info(f"密码验证结果: {'成功' if is_password_correct else '失败'}")

    if not is_password_correct:
        logger.error(f"登录失败: 用户 {user_data.username} 密码不正确")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 生成访问令牌
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    logger.info(f"用户 {user_data.username} 登录成功, 生成令牌")

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_info": {
            "id": user.id,
            "username": user.username,
            "name": user.name,
            "is_admin": user.is_admin
        }
    }

@router.get("/users/me", response_model=User)
async def read_users_me(current_user: UserModel = Depends(get_current_user)):
    return current_user

@router.post("/refresh-token", response_model=Token)
async def refresh_token(
    token: str = Depends(oauth2_scheme)
):
    """
    刷新访问令牌
    """
    logger.info("尝试刷新访问令牌")

    new_token = refresh_access_token(token)
    if not new_token:
        logger.error("令牌刷新失败")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token refresh failed",
            headers={"WWW-Authenticate": "Bearer"},
        )

    logger.info("令牌刷新成功")
    return {
        "access_token": new_token,
        "token_type": "bearer"
    }

@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    修改用户密码
    """
    # 验证当前密码是否正确
    if not verify_password(password_data.current_password, current_user.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码不正确"
        )

    # 验证新密码是否与当前密码相同
    if password_data.current_password == password_data.new_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="新密码不能与当前密码相同"
        )

    # 更新密码
    hashed_password = get_password_hash(password_data.new_password)
    current_user.password = hashed_password
    db.commit()

    return {"message": "密码修改成功"}