from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
from app.schemas.base import BaseResponseModel

class RequirementType(str, Enum):
    BUG_FIX = "Bug Fixed"
    HOT_FIX_BUG = "Hot Bug Fixed"
    NEW_FEATURE = "New Feature"

class Priority(str, Enum):
    P0 = "P0"
    P1 = "P1"
    P2 = "P2"

class RequirementStatus(str, Enum):
    DRAFT = "草稿"
    PENDING = "待处理"
    DEVELOPING = "开发中"
    TESTING = "测试中"
    VALIDATING = "验证中"
    COMPLETED = "已完成"
    REJECTED = "已拒绝"

class RequirementAction(str, Enum):
    SAVE_DRAFT = "保存草稿"
    PUBLISH = "发布需求"
    SUBMIT = "提交需求"
    CLAIM = "认领需求"
    SUBMIT_TO_TEST = "提交测试"
    WITHDRAW_TEST = "撤回测试"
    APPROVE_TEST = "通过测试"
    REJECT_TEST = "驳回测试"
    WITHDRAW_VALIDATION = "撤回验证"

# 需求基本信息
class RequirementBase(BaseModel):
    title: str = Field(..., description="需求标题")
    content: str = Field(..., description="需求内容，支持Markdown格式，包含Base64编码图片")
    type: RequirementType = Field(..., description="需求类型")
    priority: Priority = Field(..., description="优先级")

# 创建需求时的数据模型
class RequirementCreate(RequirementBase):
    project_id: int = Field(..., description="关联的项目ID")
    developer_ids: List[int] = Field(default=[], description="开发人员ID列表")
    main_branch: str = Field(..., description="主目标分支")
    other_branches: List[str] = Field(default=[], description="其他目标分支列表")
    start_date: datetime = Field(..., description="计划开始时间")
    end_date: datetime = Field(..., description="计划结束时间")
    is_draft: bool = Field(default=False, description="是否保存为草稿")

# 保存草稿时的数据模型（字段更宽松）
class RequirementDraftCreate(BaseModel):
    project_id: int = Field(..., description="关联的项目ID")
    title: str = Field(..., description="需求标题")
    content: Optional[str] = Field("", description="需求内容，支持Markdown格式，包含Base64编码图片")
    type: Optional[RequirementType] = Field(None, description="需求类型")
    priority: Optional[Priority] = Field(Priority.P2, description="优先级")
    developer_ids: List[int] = Field(default=[], description="开发人员ID列表")
    main_branch: Optional[str] = Field(None, description="主目标分支")
    other_branches: List[str] = Field(default=[], description="其他目标分支列表")
    start_date: Optional[datetime] = Field(None, description="计划开始时间")
    end_date: Optional[datetime] = Field(None, description="计划结束时间")

# 更新需求时的数据模型
class RequirementUpdate(BaseModel):
    title: Optional[str] = Field(None, description="需求标题")
    content: Optional[str] = Field(None, description="需求内容")
    type: Optional[RequirementType] = Field(None, description="需求类型")
    priority: Optional[Priority] = Field(None, description="优先级")
    status: Optional[RequirementStatus] = Field(None, description="状态")
    developer_ids: Optional[List[int]] = Field(None, description="开发人员ID列表")
    # 分支信息在修改需求时不可编辑，因此移除
    start_date: Optional[datetime] = Field(None, description="计划开始时间")
    end_date: Optional[datetime] = Field(None, description="计划结束时间")

# 需求响应模型
class RequirementResponse(RequirementBase, BaseResponseModel):
    id: int
    requirement_code: str
    project_id: int
    status: RequirementStatus
    start_date: datetime
    end_date: datetime
    submitter_id: int
    submit_time: datetime
    update_time: Optional[datetime] = None
    developers: List[dict] = []
    main_branch: str = ""
    other_branches: List[str] = []
    git_branch: Optional[str] = None

# 需求指派
class RequirementAssignmentBase(BaseModel):
    requirement_id: int
    user_id: int

class RequirementAssignmentCreate(RequirementAssignmentBase):
    pass

class RequirementAssignmentResponse(RequirementAssignmentBase, BaseResponseModel):
    id: int
    assign_time: datetime

# 需求历史记录
class RequirementHistoryBase(BaseModel):
    requirement_id: int
    current_status_code: str = Field(..., description="当前状态代码，对应RequirementStatus的枚举值")
    action_code: str = Field(..., description="动作代码，对应RequirementAction的枚举值")
    user_id: int
    remark: Optional[str] = None

class RequirementHistoryCreate(RequirementHistoryBase):
    pass

class RequirementHistoryResponse(RequirementHistoryBase, BaseResponseModel):
    id: int
    action_time: datetime

# 需求分支
class RequirementBranchBase(BaseModel):
    requirement_id: int
    branch_name: str
    is_main: bool = False

class RequirementBranchCreate(RequirementBranchBase):
    pass

class RequirementBranchResponse(RequirementBranchBase, BaseResponseModel):
    id: int