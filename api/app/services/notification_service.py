#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.utils.wechat_work import get_wechat_work_client
from app.models.user import User
from app.models.requirement import Requirement, RequirementType
from app.models.project import Project, project_testers_table
from app.core.config import settings

class NotificationService:
    """通知服务，用于发送各类通知"""

    @staticmethod
    def get_requirement_type_name(type_code: str) -> str:
        """获取需求类型的中文名称"""
        type_map = {
            "Bug Fixed": "修复漏洞",
            "Hot Bug Fixed": "紧急修复漏洞",
            "New Feature": "新功能"
        }
        return type_map.get(type_code, type_code)

    @staticmethod
    def get_notification_recipients(db: Session, developer_ids: List[int], project: Project, submitter: User = None) -> List[str]:
        """
        获取通知接收者的企业微信用户ID列表，包括：
        1. 需求相关的所有开发人员
        2. 需求所属项目的所有测试人员
        3. 需求的提交人/更新人（如果提供）

        Args:
            db: 数据库会话
            developer_ids: 开发人员ID列表
            project: 项目对象
            submitter: 提交人/更新人（可选）

        Returns:
            List[str]: 企业微信用户ID列表
        """
        wechat_user_ids = []

        # 1. 获取开发人员的企业微信用户ID
        if developer_ids:
            developers = db.query(User).filter(User.id.in_(developer_ids)).all()
            for dev in developers:
                if dev.qywx_id and dev.qywx_id not in wechat_user_ids:
                    wechat_user_ids.append(dev.qywx_id)

        # 2. 获取项目测试人员的企业微信用户ID
        if project and project.testers:
            for tester in project.testers:
                if tester.qywx_id and tester.qywx_id not in wechat_user_ids:
                    wechat_user_ids.append(tester.qywx_id)

        # 3. 如果提交人/更新人有企业微信ID且不在列表中，也加入通知列表
        if submitter and submitter.qywx_id and submitter.qywx_id not in wechat_user_ids:
            wechat_user_ids.append(submitter.qywx_id)

        # 记录日志，便于调试
        print(f"通知接收者列表: {wechat_user_ids}")
        print(f"开发人员ID列表: {developer_ids}")
        print(f"项目ID: {project.id if project else 'None'}")

        # 安全地获取项目测试人员信息
        testers_info = []
        if project and hasattr(project, 'testers') and project.testers:
            for t in project.testers:
                testers_info.append(f"{t.id}:{t.name}:{getattr(t, 'qywx_id', 'None')}")
        print(f"项目测试人员: {testers_info if testers_info else 'None'}")

        return wechat_user_ids

    @staticmethod
    def send_requirement_created_notification(
        db: Session,
        requirement: Any,
        submitter: User,
        project: Project,
        developer_ids: List[int]
    ) -> bool:
        """
        发送需求创建通知

        Args:
            db: 数据库会话
            requirement: 需求对象
            submitter: 提交人
            project: 项目对象
            developer_ids: 开发人员ID列表

        Returns:
            bool: 是否发送成功
        """
        # 获取通知接收者的企业微信用户ID
        wechat_user_ids = NotificationService.get_notification_recipients(db, developer_ids, project, submitter)

        # 如果没有有效的用户ID，则不发送通知
        if not wechat_user_ids:
            print("没有有效的企业微信用户ID，跳过通知发送")
            return False

        # 构建通知内容
        is_bug = requirement.type in ["Bug Fixed", "Hot Bug Fixed"]
        content_type = "漏洞" if is_bug else "需求"

        content = f"【{content_type}创建通知】\n"
        content += f"项目：{project.name}\n"
        content += f"编号：{requirement.requirement_code}\n"
        content += f"标题：{requirement.title}\n"
        content += f"类型：{NotificationService.get_requirement_type_name(requirement.type)}\n"
        content += f"优先级：{requirement.priority}\n"
        content += f"提交人：{submitter.name}\n"
        content += f"Git分支：{requirement.git_branch}\n"
        content += f"计划开始：{requirement.start_date.strftime('%Y-%m-%d')}\n"
        content += f"计划结束：{requirement.end_date.strftime('%Y-%m-%d')}\n"

        # 检查是否启用企业微信通知
        if not settings.WECHAT_WORK_ENABLED:
            print("企业微信通知功能已禁用，跳过通知发送")
            return False

        # 发送通知
        client = get_wechat_work_client(
            settings.WECHAT_WORK_CORPID,
            settings.WECHAT_WORK_SECRET,
            settings.WECHAT_WORK_AGENTID
        )
        return client.send_text_message(wechat_user_ids, content)

    @staticmethod
    def send_requirement_updated_notification(
        db: Session,
        requirement: Any,
        updater: User,
        project: Project,
        developer_ids: List[int],
        updated_fields: List[str]
    ) -> bool:
        """
        发送需求更新通知

        Args:
            db: 数据库会话
            requirement: 需求对象
            updater: 更新人
            project: 项目对象
            developer_ids: 开发人员ID列表
            updated_fields: 更新的字段列表

        Returns:
            bool: 是否发送成功
        """
        # 获取通知接收者的企业微信用户ID
        wechat_user_ids = NotificationService.get_notification_recipients(db, developer_ids, project, updater)

        # 如果没有有效的用户ID，则不发送通知
        if not wechat_user_ids:
            print("没有有效的企业微信用户ID，跳过通知发送")
            return False

        # 构建通知内容
        is_bug = requirement.type in ["Bug Fixed", "Hot Bug Fixed"]
        content_type = "漏洞" if is_bug else "需求"

        content = f"【{content_type}更新通知】\n"
        content += f"项目：{project.name}\n"
        content += f"编号：{requirement.requirement_code}\n"
        content += f"标题：{requirement.title}\n"
        content += f"更新人：{updater.name}\n"

        # 添加更新的字段信息
        if updated_fields:
            content += "更新内容：\n"
            field_map = {
                "title": "标题",
                "content": "内容",
                "type": "类型",
                "priority": "优先级",
                "status": "状态",
                "start_date": "计划开始日期",
                "end_date": "计划结束日期"
            }
            for field in updated_fields:
                if field in field_map:
                    content += f"- {field_map[field]}\n"

        # 检查是否启用企业微信通知
        if not settings.WECHAT_WORK_ENABLED:
            print("企业微信通知功能已禁用，跳过通知发送")
            return False

        # 发送通知
        client = get_wechat_work_client(
            settings.WECHAT_WORK_CORPID,
            settings.WECHAT_WORK_SECRET,
            settings.WECHAT_WORK_AGENTID
        )
        return client.send_text_message(wechat_user_ids, content)

    @staticmethod
    def send_requirement_deleted_notification(
        db: Session,
        requirement: Any,
        deleter: User,
        project: Project,
        notify_user_ids: List[int]
    ) -> bool:
        """
        发送需求删除通知

        Args:
            db: 数据库会话
            requirement: 需求对象
            deleter: 删除人
            project: 项目对象
            notify_user_ids: 需要通知的用户ID列表

        Returns:
            bool: 是否发送成功
        """
        # 获取通知接收者的企业微信用户ID
        wechat_user_ids = []
        if notify_user_ids:
            users = db.query(User).filter(User.id.in_(notify_user_ids)).all()
            for user in users:
                if user.qywx_id and user.qywx_id not in wechat_user_ids:
                    wechat_user_ids.append(user.qywx_id)

        # 如果没有有效的用户ID，则不发送通知
        if not wechat_user_ids:
            print("没有有效的企业微信用户ID，跳过通知发送")
            return False

        # 构建通知内容
        is_bug = requirement.type in ["Bug Fixed", "Hot Bug Fixed"]
        content_type = "漏洞" if is_bug else "需求"

        content = f"【{content_type}删除通知】\n"
        content += f"项目：{project.name}\n"
        content += f"编号：{requirement.requirement_code}\n"
        content += f"标题：{requirement.title}\n"
        content += f"类型：{NotificationService.get_requirement_type_name(requirement.type)}\n"
        content += f"优先级：{requirement.priority}\n"
        content += f"删除人：{deleter.name}\n"
        content += f"删除时间：{requirement.update_time.strftime('%Y-%m-%d %H:%M:%S') if requirement.update_time else '未知'}\n"
        content += f"原状态：{requirement.status}\n"

        if requirement.git_branch:
            content += f"相关分支：{requirement.git_branch}（已删除）\n"

        # 检查是否启用企业微信通知
        if not settings.WECHAT_WORK_ENABLED:
            print("企业微信通知功能已禁用，跳过通知发送")
            return False

        # 发送通知
        client = get_wechat_work_client(
            settings.WECHAT_WORK_CORPID,
            settings.WECHAT_WORK_SECRET,
            settings.WECHAT_WORK_AGENTID
        )
        return client.send_text_message(wechat_user_ids, content)

    @staticmethod
    def send_requirement_status_change_notification(
        db: Session,
        requirement: Any,
        operator: User,
        project: Project,
        action: str,
        new_status: str,
        reason: str = None
    ) -> bool:
        """
        发送需求状态变更通知

        Args:
            db: 数据库会话
            requirement: 需求对象
            operator: 操作人
            project: 项目对象
            action: 操作动作（中文）
            new_status: 新状态（中文）
            reason: 操作理由（可选）

        Returns:
            bool: 是否发送成功
        """
        # 获取开发人员和测试人员信息
        developer_ids = []
        if hasattr(requirement, 'developers') and requirement.developers:
            developer_ids = [dev.id if hasattr(dev, 'id') else dev for dev in requirement.developers]
        else:
            # 从数据库查询开发人员
            from app.models.requirement import RequirementAssignment
            developer_records = db.query(User).join(
                RequirementAssignment,
                User.id == RequirementAssignment.user_id
            ).filter(
                RequirementAssignment.requirement_id == requirement.id
            ).all()
            developer_ids = [dev.id for dev in developer_records]

        # 根据操作类型决定通知对象
        notify_user_ids = []

        if action in ["认领需求"]:
            # 认领需求：通知管理员
            admin_users = db.query(User).filter(User.is_admin == True).all()
            notify_user_ids = [admin.id for admin in admin_users]

        elif action in ["提交测试"]:
            # 提交测试：通知管理员和测试人员
            admin_users = db.query(User).filter(User.is_admin == True).all()
            tester_records = db.query(User).join(
                project_testers_table,
                User.id == project_testers_table.c.user_id
            ).filter(
                project_testers_table.c.project_id == project.id
            ).all()
            notify_user_ids = [admin.id for admin in admin_users] + [tester.id for tester in tester_records]

        elif action in ["撤回测试"]:
            # 撤回测试：通知管理员和测试人员
            admin_users = db.query(User).filter(User.is_admin == True).all()
            tester_records = db.query(User).join(
                project_testers_table,
                User.id == project_testers_table.c.user_id
            ).filter(
                project_testers_table.c.project_id == project.id
            ).all()
            notify_user_ids = [admin.id for admin in admin_users] + [tester.id for tester in tester_records]

        elif action in ["通过测试"]:
            # 通过测试：通知管理员
            admin_users = db.query(User).filter(User.is_admin == True).all()
            notify_user_ids = [admin.id for admin in admin_users]

        elif action in ["驳回测试"]:
            # 驳回测试：通知管理员和开发人员
            admin_users = db.query(User).filter(User.is_admin == True).all()
            notify_user_ids = [admin.id for admin in admin_users] + developer_ids

        elif action in ["撤回验证"]:
            # 撤回验证：通知管理员和测试人员
            admin_users = db.query(User).filter(User.is_admin == True).all()
            tester_records = db.query(User).join(
                project_testers_table,
                User.id == project_testers_table.c.user_id
            ).filter(
                project_testers_table.c.project_id == project.id
            ).all()
            notify_user_ids = [admin.id for admin in admin_users] + [tester.id for tester in tester_records]

        elif action in ["驳回验证"]:
            # 驳回验证：通知开发人员和测试人员
            tester_records = db.query(User).join(
                project_testers_table,
                User.id == project_testers_table.c.user_id
            ).filter(
                project_testers_table.c.project_id == project.id
            ).all()
            notify_user_ids = developer_ids + [tester.id for tester in tester_records]

        # 去重
        notify_user_ids = list(set(notify_user_ids))

        # 获取通知接收者的企业微信用户ID
        wechat_user_ids = []
        if notify_user_ids:
            users = db.query(User).filter(User.id.in_(notify_user_ids)).all()
            for user in users:
                if user.qywx_id and user.qywx_id not in wechat_user_ids:
                    wechat_user_ids.append(user.qywx_id)

        # 如果没有有效的用户ID，则不发送通知
        if not wechat_user_ids:
            print("没有有效的企业微信用户ID，跳过通知发送")
            return False

        # 构建通知内容
        is_bug = requirement.type in ["Bug Fixed", "Hot Bug Fixed"]
        content_type = "漏洞" if is_bug else "需求"

        content = f"【{content_type}状态变更通知】\n"
        content += f"项目：{project.name}\n"
        content += f"编号：{requirement.requirement_code}\n"
        content += f"标题：{requirement.title}\n"
        content += f"操作：{action}\n"
        content += f"操作人：{operator.name}\n"
        content += f"新状态：{new_status}\n"

        if reason:
            content += f"理由：{reason}\n"

        if requirement.git_branch:
            content += f"相关分支：{requirement.git_branch}\n"

        # 检查是否启用企业微信通知
        if not settings.WECHAT_WORK_ENABLED:
            print("企业微信通知功能已禁用，跳过通知发送")
            return False

        # 发送通知
        client = get_wechat_work_client(
            settings.WECHAT_WORK_CORPID,
            settings.WECHAT_WORK_SECRET,
            settings.WECHAT_WORK_AGENTID
        )
        return client.send_text_message(wechat_user_ids, content)