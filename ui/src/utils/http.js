import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// 创建axios实例
const http = axios.create({
  baseURL: '/api', // 将由开发服务器代理
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  async response => {
    // 每次成功的API调用后都刷新token（除了刷新token的请求本身）
    const token = localStorage.getItem('token')
    if (token && response.config.url !== '/refresh-token') {
      try {
        // 创建一个新的axios实例来避免递归调用拦截器
        const refreshHttp = axios.create({
          baseURL: '/api',
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        })

        const refreshResponse = await refreshHttp.post('/refresh-token')
        if (refreshResponse.data && refreshResponse.data.access_token) {
          localStorage.setItem('token', refreshResponse.data.access_token)
          console.log('Token已自动刷新')
        }
      } catch (refreshError) {
        // 刷新失败，忽略错误（可能是token已经过期）
        console.log('Token刷新失败:', refreshError.response?.status)
      }
    }

    // 返回响应数据
    return response.data
  },
  async error => {
    // 处理错误响应
    const { response } = error
    if (response) {
      // 根据状态码处理不同错误
      switch (response.status) {
        case 401:
          // 401错误，清除token并跳转到登录页
          console.log('检测到401错误，准备跳转到登录页')
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')  // 同时清除用户信息
          setTimeout(() => {
            // 使用延时确保先删除完token和userInfo
            window.location.href = '/login'
          }, 100)
          ElMessage.error('登录已过期，请重新登录')
          break
        case 403:
          ElMessage.error('权限不足，无法访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(response.data?.detail || '请求失败')
      }
    } else {
      // 网络错误或请求被取消
      ElMessage.error('网络错误，请检查您的网络连接')
    }
    return Promise.reject(error)
  }
)

export { http }