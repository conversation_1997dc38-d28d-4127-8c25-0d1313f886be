<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="handleClose"
    class="requirement-form-dialog"
  >
    <div class="form-container">
      <form @submit.prevent="handleSubmit">
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Document /></el-icon>
            <h3 class="section-title">基本信息</h3>
          </div>

          <!-- 项目选择 - 仅在上报漏洞模式下显示 -->
          <div v-if="mode === 'bug'" class="form-field">
            <label class="form-label">
              <span class="required">*</span> 所属项目
            </label>
            <el-select
              v-model="formData.project_id"
              placeholder="请选择项目"
              @change="handleProjectChange"
              class="form-control"
            >
              <el-option
                v-for="item in projectsList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>

          <!-- 标题 -->
          <div class="form-field">
            <label class="form-label">
              <span class="required">*</span> {{ titleLabel }}
            </label>
            <el-input
              v-model="formData.title"
              :placeholder="titlePlaceholder"
              class="form-control"
            ></el-input>
          </div>

          <!-- 类型和优先级 -->
          <div class="form-row">
            <div class="form-field">
              <label class="form-label">
                <span class="required">*</span> {{ typeLabel }}
              </label>
              <el-select
                v-model="formData.type"
                :placeholder="`请选择${mode === 'bug' ? '漏洞' : '需求'}类型`"
                class="form-control"
              >
                <el-option
                  v-for="(label, value) in typeOptions"
                  :key="value"
                  :label="label"
                  :value="value"
                ></el-option>
              </el-select>
            </div>

            <div class="form-field">
              <label class="form-label">
                <span class="required">*</span> 优先级
              </label>
              <div v-if="mode === 'bug'" class="priority-auto">
                <el-tag type="danger" size="large">P0</el-tag>
                <span class="auto-text">（漏洞自动设置为最高优先级）</span>
              </div>
              <el-select
                v-else
                v-model="formData.priority"
                placeholder="请选择优先级"
                class="form-control"
              >
                <el-option
                  v-for="(label, value) in priorityOptions"
                  :key="value"
                  :label="label"
                  :value="value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>

        <!-- 详细内容区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><EditPen /></el-icon>
            <h3 class="section-title">详细内容</h3>
          </div>

          <div class="form-field">
            <label class="form-label">
              <span class="required">*</span> {{ contentLabel }}
            </label>
            <mavon-editor
              v-model="formData.content"
              :toolbars="markdownOption"
              :placeholder="contentPlaceholder"
              style="min-height: 300px; max-height: 300px; width: 100%"
              class="markdown-theme"
              :toolbarsFlag="false"
              @imgAdd="handleImgAdd"
            ></mavon-editor>
          </div>
        </div>

        <!-- 分配信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><User /></el-icon>
            <h3 class="section-title">分配信息</h3>
          </div>

          <div class="form-field">
            <label class="form-label">
              <span class="required">*</span> 开发人员
            </label>
            <el-select
              v-model="formData.developer_ids"
              multiple
              placeholder="请选择开发人员"
              :disabled="mode === 'bug' && !formData.project_id"
              class="form-control"
            >
              <el-option
                v-for="dev in projectDevelopers"
                :key="dev.id"
                :label="getDeveloperLabel(dev)"
                :value="dev.id"
              ></el-option>
            </el-select>
          </div>
        </div>

        <!-- 分支信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Share /></el-icon>
            <h3 class="section-title">分支信息</h3>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label class="form-label">
                <span class="required">*</span> 主目标分支
              </label>
              <el-select
                v-model="formData.main_branch"
                placeholder="请选择主目标分支"
                :disabled="mode === 'bug' && !formData.project_id"
                class="form-control"
                @change="handleMainBranchChange"
              >
                <el-option
                  v-for="branch in projectBranches"
                  :key="branch.name"
                  :label="branch.name"
                  :value="branch.name"
                ></el-option>
              </el-select>
            </div>

            <div class="form-field">
              <label class="form-label">其他目标分支</label>
              <el-select
                v-model="formData.other_branches"
                multiple
                placeholder="请选择其他目标分支（可选）"
                :disabled="mode === 'bug' && (!formData.project_id || !formData.main_branch)"
                class="form-control"
              >
                <el-option
                  v-for="branch in filteredOtherBranches"
                  :key="branch.name"
                  :label="branch.name"
                  :value="branch.name"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>

        <!-- 时间计划区域 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Clock /></el-icon>
            <h3 class="section-title">时间计划</h3>
          </div>

          <div v-if="mode === 'bug'" class="time-auto">
            <el-alert
              title="时间自动设置"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <p>漏洞报告的时间将自动设置：</p>
                <ul>
                  <li>开始时间：当前时间</li>
                  <li>结束时间：30天后</li>
                </ul>
              </template>
            </el-alert>
          </div>

          <div v-else class="form-field">
            <label class="form-label">
              <span class="required">*</span> 计划时间
            </label>
            <div class="date-range">
              <el-date-picker
                v-model="formData.start_date"
                type="date"
                placeholder="计划开始日期"
                class="date-picker"
              ></el-date-picker>
              <span class="date-separator">至</span>
              <el-date-picker
                v-model="formData.end_date"
                type="date"
                placeholder="计划结束日期"
                class="date-picker"
              ></el-date-picker>
            </div>
          </div>
        </div>
      </form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          size="large"
        >
          {{ submitButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, toRefs } from 'vue';
import { Document, EditPen, User, Share, Clock } from '@element-plus/icons-vue';
import 'mavon-editor/dist/css/index.css';

export default {
  name: 'RequirementFormDialog',
  components: {
    Document,
    EditPen,
    User,
    Share,
    Clock
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'requirement', // 'requirement' 或 'bug'
      validator: (value) => ['requirement', 'bug'].includes(value)
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    projectsList: {
      type: Array,
      default: () => []
    },
    projectDevelopers: {
      type: Array,
      default: () => []
    },
    projectBranches: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:visible', 'submit', 'project-change'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const submitting = ref(false);

    // 使用toRefs确保props的响应性
    const { projectDevelopers, projectBranches } = toRefs(props);



    // 表单数据
    const formData = ref({
      project_id: props.projectId || '',
      title: '',
      content: '',
      type: props.mode === 'bug' ? 'Bug Fixed' : '',
      priority: 'P2',
      developer_ids: [],
      main_branch: '',
      other_branches: [],
      start_date: new Date(),
      end_date: ''
    });

    // 计算属性 - 根据模式显示不同的文本
    const dialogTitle = computed(() => {
      return props.mode === 'bug' ? '上报漏洞' : '添加需求';
    });

    const titleLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞标题' : '需求标题';
    });

    const titlePlaceholder = computed(() => {
      return props.mode === 'bug' ? '请输入漏洞标题' : '请输入需求标题';
    });

    const contentLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞内容' : '需求内容';
    });

    const contentPlaceholder = computed(() => {
      return props.mode === 'bug' ? '请输入漏洞详细内容，支持Markdown格式' : '请输入需求详细内容，支持Markdown格式';
    });

    const typeLabel = computed(() => {
      return props.mode === 'bug' ? '漏洞类型' : '需求类型';
    });

    const submitButtonText = computed(() => {
      return props.mode === 'bug' ? '提交漏洞报告' : '创建需求';
    });

    // 需求类型选项
    const requirementTypes = {
      'Bug Fixed': '修复漏洞',
      'Hot Bug Fixed': '紧急修复漏洞',
      'New Feature': '新功能'
    };

    // 根据模式显示不同的类型选项
    const typeOptions = computed(() => {
      if (props.mode === 'bug') {
        return {
          'Bug Fixed': '修复漏洞',
          'Hot Bug Fixed': '紧急修复漏洞'
        };
      } else {
        return requirementTypes;
      }
    });

    // 优先级选项
    const priorityOptions = {
      'P0': 'P0（最高优先级）',
      'P1': 'P1（高优先级）',
      'P2': 'P2（普通优先级）'
    };

    // 过滤后的其他目标分支（排除主目标分支）
    const filteredOtherBranches = computed(() => {
      if (!formData.value.main_branch) return projectBranches.value;
      return projectBranches.value.filter(branch => branch.name !== formData.value.main_branch);
    });

    // Markdown编辑器配置
    const markdownOption = {
      bold: true,
      italic: true,
      header: true,
      underline: true,
      strikethrough: true,
      mark: true,
      superscript: true,
      subscript: true,
      quote: true,
      ol: true,
      ul: true,
      link: true,
      imagelink: true,
      code: true,
      table: true,
      fullscreen: true,
      readmodel: true,
      htmlcode: true,
      help: true,
      undo: true,
      redo: true,
      trash: true,
      save: true,
      navigation: true,
      alignleft: true,
      aligncenter: true,
      alignright: true,
      subfield: true,
      preview: true,
      defaultOpen: 'edit',
      theme: 'dark',
      codeStyle: 'atom-one-dark'
    };

    // 获取开发人员显示标签
    const getDeveloperLabel = (dev) => {
      return `${dev.name} (${dev.username})`;
    };

    // 处理项目变更（仅在漏洞模式下）
    const handleProjectChange = (projectId) => {
      // 清空相关字段
      formData.value.developer_ids = [];
      formData.value.main_branch = '';
      formData.value.other_branches = [];

      // 通知父组件项目变更
      emit('project-change', projectId);
    };

    // 处理主目标分支变更
    const handleMainBranchChange = (value) => {
      // 如果新选择的主目标分支已经在其他目标分支中，则从其他目标分支中移除
      if (formData.value.other_branches.includes(value)) {
        formData.value.other_branches = formData.value.other_branches.filter(branch => branch !== value);
      }
    };

    // 监听主目标分支变化
    watch(() => formData.value.main_branch, (newVal) => {
      if (newVal && formData.value.other_branches.includes(newVal)) {
        formData.value.other_branches = formData.value.other_branches.filter(branch => branch !== newVal);
      }
    });

    // 监听其他目标分支变化，确保主目标分支不在其中
    watch(() => formData.value.other_branches, (newVal) => {
      if (newVal.includes(formData.value.main_branch) && formData.value.main_branch) {
        formData.value.main_branch = '';
      }
    }, { deep: true });

    // 监听props.projectId变化
    watch(() => props.projectId, (newVal) => {
      if (newVal) {
        formData.value.project_id = newVal;
      }
    });

    // 处理图片添加
    const handleImgAdd = (pos, file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target.result;
        const imgTag = `<img src="${base64}" alt="image" style="max-width: 100%; height: auto;" />`;

        // 替换markdown语法为HTML标签
        const content = formData.value.content;
        const beforeContent = content.substring(0, pos);
        const afterContent = content.substring(pos);
        formData.value.content = beforeContent + imgTag + afterContent;
      };
      reader.readAsDataURL(file);
    };

    // 重置表单
    const resetForm = () => {
      formData.value = {
        project_id: props.projectId || '',
        title: '',
        content: '',
        type: props.mode === 'bug' ? 'Bug Fixed' : '',
        priority: 'P2',
        developer_ids: [],
        main_branch: '',
        other_branches: [],
        start_date: new Date(),
        end_date: ''
      };
    };

    // 关闭对话框
    const handleClose = () => {
      resetForm();
      emit('update:visible', false);
    };

    // 表单验证规则
    const formRules = computed(() => {
      const baseRules = {
        title: [{ required: true, message: `请输入${props.mode === 'bug' ? '漏洞' : '需求'}标题`, trigger: 'blur' }],
        content: [{ required: true, message: `请输入${props.mode === 'bug' ? '漏洞' : '需求'}内容`, trigger: 'blur' }],
        type: [{ required: true, message: `请选择${props.mode === 'bug' ? '漏洞' : '需求'}类型`, trigger: 'change' }],
        developer_ids: [{ required: true, message: '请选择开发人员', trigger: 'change' }],
        main_branch: [{ required: true, message: '请选择主目标分支', trigger: 'change' }]
      };

      if (props.mode === 'bug') {
        baseRules.project_id = [{ required: true, message: '请选择项目', trigger: 'change' }];
      } else {
        baseRules.start_date = [{ required: true, message: '请选择计划开始日期', trigger: 'change' }];
        baseRules.end_date = [{ required: true, message: '请选择计划结束日期', trigger: 'change' }];
      }

      return baseRules;
    });

    // 提交表单
    const handleSubmit = async () => {
      try {
        submitting.value = true;

        // 检查图片是否已完成处理
        const markdownImgRegex = /!\[.*?\]\(.*?\)/g;
        if (markdownImgRegex.test(formData.value.content)) {
          throw new Error('有图片尚未完成处理，请稍等片刻再提交');
        }

        // 构建提交数据
        const submitData = { ...formData.value };

        if (props.mode === 'bug') {
          // 漏洞模式：自动设置优先级和时间
          const now = new Date();
          const endDate = new Date(now);
          endDate.setDate(endDate.getDate() + 30);

          submitData.priority = 'P0';
          submitData.start_date = now.toISOString().split('T')[0] + ' 00:00:00';
          submitData.end_date = endDate.toISOString().split('T')[0] + ' 23:59:59';
        } else {
          // 需求模式：格式化日期
          if (submitData.start_date && submitData.start_date instanceof Date) {
            submitData.start_date = submitData.start_date.toISOString().split('T')[0] + ' 00:00:00';
          }
          if (submitData.end_date && submitData.end_date instanceof Date) {
            submitData.end_date = submitData.end_date.toISOString().split('T')[0] + ' 23:59:59';
          }
          // 使用传入的项目ID
          submitData.project_id = props.projectId;
          // 转换开发人员字段名（需求模式下使用developers字段）
          const developers = submitData.developer_ids;
          delete submitData.developer_ids;
          // 使用Object.assign来避免类型错误
          Object.assign(submitData, { developers });
        }

        // 发送提交事件
        emit('submit', submitData);

        // 重置表单并关闭对话框
        resetForm();
        emit('update:visible', false);

      } catch (error) {
        console.error('表单提交失败:', error);
        throw error;
      } finally {
        submitting.value = false;
      }
    };

    return {
      // Props (for template access)
      mode: props.mode,
      projectDevelopers,
      projectBranches,

      // Refs and reactive data
      formRef,
      formData,
      formRules,
      submitting,

      // Computed properties
      dialogTitle,
      titleLabel,
      titlePlaceholder,
      contentLabel,
      contentPlaceholder,
      typeLabel,
      submitButtonText,
      typeOptions,
      requirementTypes,
      priorityOptions,
      filteredOtherBranches,
      markdownOption,

      // Methods
      getDeveloperLabel,
      handleProjectChange,
      handleMainBranchChange,
      handleImgAdd,
      handleClose,
      handleSubmit,
      resetForm
    };
  }
};
</script>

<style scoped>
/* 遵循UI规范文档的对话框样式 */
.requirement-form-dialog {
  --el-dialog-border-radius: 10px;
}

.requirement-form-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px 10px 0 0;
  padding: 20px 24px;
}

.requirement-form-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
}

.requirement-form-dialog :deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
}

.requirement-form-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 18px;
}

.requirement-form-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.form-container {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 分区域样式 */
.form-section {
  margin-bottom: 32px;
  background: #fafbfc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.form-section:hover {
  border-color: #c6e2ff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e4e7ed;
}

.section-icon {
  font-size: 20px;
  color: #409eff;
  margin-right: 12px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 表单字段样式 */
.form-field {
  margin-bottom: 20px;
}

.form-field:last-child {
  margin-bottom: 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.form-control {
  width: 100%;
}

/* 优先级自动设置样式 */
.priority-auto {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auto-text {
  color: #909399;
  font-size: 13px;
}

/* 时间自动设置样式 */
.time-auto {
  background: #f0f9ff;
  border-radius: 8px;
  padding: 16px;
}

.time-auto :deep(.el-alert) {
  background: transparent;
  border: none;
  padding: 0;
}

.time-auto :deep(.el-alert__content) {
  color: #409eff;
}

.time-auto ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.time-auto li {
  margin-bottom: 4px;
  color: #606266;
}

/* 日期范围样式 */
.date-range {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-picker {
  flex: 1;
}

.date-separator {
  color: #909399;
  font-weight: 500;
}

/* 对话框底部样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: #fafbfc;
  border-top: 1px solid #e4e7ed;
}

/* Markdown编辑器样式优化 */
.form-field :deep(.v-note-wrapper) {
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s ease;
}

.form-field :deep(.v-note-wrapper:hover) {
  border-color: #c0c4cc;
}

.form-field :deep(.v-note-wrapper.focus) {
  border-color: #409eff;
}

/* 确保mavon-editor的textarea背景为白色 */
.markdown-theme :deep(.v-note-edit.divarea-wrapper) {
  background-color: #fff !important;
}

.markdown-theme :deep(.auto-textarea-wrapper textarea) {
  background-color: #fff !important;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .requirement-form-dialog {
    width: 95% !important;
    margin: 5vh auto;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .date-range {
    flex-direction: column;
    align-items: stretch;
  }

  .date-separator {
    text-align: center;
    margin: 8px 0;
  }

  .form-container {
    padding: 16px;
    max-height: 60vh;
  }

  .form-section {
    padding: 16px;
    margin-bottom: 20px;
  }
}

/* 选择器样式优化 */
.form-control :deep(.el-select) {
  width: 100%;
}

.form-control :deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.form-control :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.form-control :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 标签样式 */
.form-field :deep(.el-tag) {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 按钮样式 */
.dialog-footer :deep(.el-button) {
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
}

.dialog-footer :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
}

.dialog-footer :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
}
</style>