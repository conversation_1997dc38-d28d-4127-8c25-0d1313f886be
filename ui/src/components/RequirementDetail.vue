<template>
  <!-- 需求详情对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="需求详情"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    @closed="handleDialogClosed"
    class="requirement-dialog"
  >
    <div v-if="loading" class="dialog-loading">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="requirement" class="requirement-detail">
      <!-- 需求基本信息卡片 -->
      <div class="card">
        <div class="requirement-header">
          <div class="requirement-title">{{ requirement.title }}</div>
          <div class="requirement-id">需求编号：{{ requirement.requirement_code }} | 项目：{{ requirement.project_name }}</div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">类型</div>
            <div class="info-value">
              <span class="badge" :class="getTypeBadgeClass(requirement.type)">
                {{ requirementTypes[requirement.type] || requirement.type }}
              </span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">优先级</div>
            <div class="info-value" :class="getPriorityClass(requirement.priority)">
              {{ requirement.priority }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">状态</div>
            <div class="info-value">
              <span class="badge" :class="getStatusBadgeClass(requirement.status)">
                {{ requirement.status }}
              </span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">计划开始时间</div>
            <div class="info-value">{{ formatToDate(requirement.start_date) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">计划完成时间</div>
            <div class="info-value">{{ formatToDate(requirement.end_date) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">提交时间</div>
            <div class="info-value">{{ formatToDateTime(requirement.submit_time) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">更新时间</div>
            <div class="info-value">{{ formatToDateTime(requirement.update_time) }}</div>
          </div>
        </div>

        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">提交人</div>
            <div class="info-value">{{ requirement.submitter?.name || '未知' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">开发人员</div>
            <div class="people-list">
              <span v-for="dev in requirement.developers" :key="dev.id" class="person-tag">
                {{ dev.name }}
              </span>
              <span v-if="!requirement.developers || requirement.developers.length === 0" class="person-tag">无</span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">测试人员</div>
            <div class="people-list">
              <span v-for="tester in requirement.testers" :key="tester.id" class="person-tag">
                {{ tester.name }}
              </span>
              <span v-if="!requirement.testers || requirement.testers.length === 0" class="person-tag">无</span>
            </div>
          </div>
        </div>

        <div class="info-grid" v-if="!restrictContent || canViewFullContentComputed">
          <div class="info-item">
            <div class="info-label">主目标分支</div>
            <div class="info-value">
              <span class="badge badge-green">{{ requirement.main_branch || '-' }}</span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">Git分支</div>
            <div class="info-value">
              <span class="badge badge-blue">{{ requirement.git_branch || '-' }}</span>
              <el-button v-if="requirement.git_branch" size="small" type="primary" plain @click="copyToClipboard(requirement.git_branch)" class="copy-btn">
                复制
              </el-button>
            </div>
          </div>
        </div>

        <div class="info-grid" v-if="requirement.other_branches && requirement.other_branches.length > 0 && (!restrictContent || canViewFullContentComputed)">
          <div class="info-item">
            <div class="info-label">其他目标分支</div>
            <div class="info-value">
              <span v-for="(branch, index) in requirement.other_branches" :key="branch">
                {{ branch }}{{ index < requirement.other_branches.length - 1 ? '，' : '' }}
              </span>
            </div>
          </div>
        </div>

        <div class="info-grid" v-if="restrictContent && !canViewFullContentComputed">
          <div class="info-item">
            <div class="info-label">分支信息</div>
            <div class="info-value">
              <el-alert
                title="分支信息仅在认领需求后可见"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 需求内容卡片 -->
      <div class="card">
        <div class="card-title">需求内容</div>

        <div v-if="!restrictContent || canViewFullContentComputed" class="content-section">
          <div v-if="requirement.content" class="content-text" v-html="renderMarkdown(requirement.content)"></div>
          <div v-else class="content-text">暂无详细内容</div>
        </div>
        <div v-else class="content-alert">
          <el-alert
            title="需求内容仅在认领需求后可见"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <!-- 历史记录卡片 -->
      <div class="card" v-if="requirement.histories && requirement.histories.length > 0">
        <div class="card-title">历史记录</div>

        <div class="timeline">
          <div class="timeline-item" v-for="history in requirement.histories" :key="history.id">
            <div class="timeline-header">
              <div class="timeline-user">{{ history.user.name }}</div>
              <div class="timeline-action">{{ history.action }}</div>
              <span v-if="history.current_status" class="badge" :class="getStatusBadgeClass(history.current_status)">
                {{ history.current_status }}
              </span>
              <div class="timeline-time">{{ formatToDateTime(history.action_time) }}</div>
            </div>
            <div class="history-remark" v-if="history.remark">
              备注: {{ history.remark }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <!-- 操作按钮区域 -->
        <div class="action-buttons" v-if="requirement">
          <!-- 草稿状态：只有管理员可以操作 -->
          <template v-if="requirement.status === '草稿' && userRole === 'admin'">
            <el-button type="primary" @click="$emit('edit', requirement)">编辑</el-button>
            <el-button type="success" @click="$emit('publish', requirement)">发布</el-button>
            <el-button type="danger" @click="$emit('delete', requirement)">删除</el-button>
          </template>

          <!-- 待认领状态：开发人员可以认领 -->
          <template v-if="requirement.status === '待处理' && userRole === 'developer' && isRequirementDeveloper">
            <el-button type="primary" @click="$emit('claim', requirement)">认领任务</el-button>
          </template>

          <!-- 开发中状态：开发人员可以提交测试 -->
          <template v-if="requirement.status === '开发中' && userRole === 'developer' && isRequirementDeveloper">
            <el-button type="warning" @click="$emit('submitToTest', requirement)">提交测试</el-button>
          </template>

          <!-- 测试中状态：开发人员可以撤回测试，测试人员可以测试操作 -->
          <template v-if="requirement.status === '测试中'">
            <template v-if="userRole === 'developer' && isRequirementDeveloper">
              <el-button type="info" @click="$emit('withdrawFromTest', requirement)">撤回测试</el-button>
            </template>
            <template v-if="userRole === 'tester' && isRequirementTester">
              <el-button type="success" @click="$emit('approveTest', requirement)">通过测试</el-button>
              <el-button type="danger" @click="$emit('rejectTest', requirement)">驳回测试</el-button>
            </template>
          </template>

          <!-- 验证中状态：只有管理员可以验证操作 -->
          <template v-if="requirement.status === '验证中' && userRole === 'admin'">
            <el-button type="success" @click="$emit('approveValidation', requirement)">通过验证</el-button>
            <el-button type="danger" @click="$emit('rejectValidation', requirement)">驳回验证</el-button>
          </template>
        </div>

        <el-button @click="closeDialog" class="close-btn">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import { marked } from 'marked';
import { ElMessage } from 'element-plus';
import { formatToDate, formatToDateTime } from '../utils/dateUtils';
import { getRequirementDetail } from '../api/project';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  requirementId: {
    type: [Number, String],
    default: null
  },
  restrictContent: {
    type: Boolean,
    default: false
  },
  canViewFullContent: {
    type: Boolean,
    default: true
  },
  userRole: {
    type: String,
    required: true,
    validator: (value) => ['admin', 'developer', 'tester', 'project_manager'].includes(value)
  },
  currentUserId: {
    type: Number,
    required: true
  }
});

const emit = defineEmits([
  'update:visible', 'closed', 'edit', 'publish', 'delete', 'claim', 'submitToTest',
  'withdrawFromTest', 'approveTest', 'rejectTest', 'approveValidation', 'rejectValidation'
]);

// 内部状态
const dialogVisible = ref(false);
const requirement = ref(null);
const loading = ref(false);

// 需求类型映射
const requirementTypes = {
  'Bug Fixed': '修复漏洞',
  'Hot Bug Fixed': '紧急修复漏洞',
  'New Feature': '新功能'
};

// 检查当前用户是否是需求的开发人员
const isRequirementDeveloper = computed(() => {
  if (!requirement.value || !requirement.value.developers) return false;
  if (Array.isArray(requirement.value.developers)) {
    return requirement.value.developers.some(dev =>
      (typeof dev === 'object' ? dev.id : dev) == props.currentUserId
    );
  }
  return requirement.value.developers == props.currentUserId;
});

// 检查当前用户是否是需求的测试人员
const isRequirementTester = computed(() => {
  if (!requirement.value || !requirement.value.testers) return false;
  if (Array.isArray(requirement.value.testers)) {
    return requirement.value.testers.some(tester =>
      (typeof tester === 'object' ? tester.id : tester) == props.currentUserId
    );
  }
  return requirement.value.testers == props.currentUserId;
});

// 检查开发人员是否已认领需求（用于判断是否可以查看详细内容）
const isDeveloperClaimed = computed(() => {
  if (!requirement.value || props.userRole !== 'developer') return true;

  // 如果不是待处理状态，说明已经认领过了
  if (requirement.value.status !== '待处理') return true;

  // 如果是待处理状态，检查当前用户是否是开发人员且已认领
  return isRequirementDeveloper.value;
});

// 检查是否可以查看完整内容（针对开发人员在待认领状态的限制）
const canViewFullContentComputed = computed(() => {
  // 如果props中明确指定了canViewFullContent，使用该值
  if (props.hasOwnProperty('canViewFullContent')) {
    return props.canViewFullContent;
  }

  // 管理员、项目管理部、测试人员可以查看完整内容
  if (props.userRole === 'admin' || props.userRole === 'project_manager' || props.userRole === 'tester') {
    return true;
  }

  // 开发人员需要检查是否已认领
  if (props.userRole === 'developer') {
    return isDeveloperClaimed.value;
  }

  return true;
});

// 监听visible属性变化
watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue;
  if (newValue && props.requirementId) {
    fetchRequirementDetail();
  }
});

// 监听dialogVisible变化，同步回父组件
watch(dialogVisible, (newValue) => {
  emit('update:visible', newValue);
});

// 获取需求详情
const fetchRequirementDetail = async () => {
  if (!props.requirementId) return;

  try {
    loading.value = true;
    const response = await getRequirementDetail(props.requirementId);
    requirement.value = response;
    console.log('需求详情:', requirement.value);
  } catch (error) {
    console.error('获取需求详情失败:', error);
    ElMessage.error('获取需求详情失败: ' + (error.message || '未知错误'));
    dialogVisible.value = false;
  } finally {
    loading.value = false;
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 对话框关闭后的处理
const handleDialogClosed = () => {
  requirement.value = null;
  emit('closed');
};

// 获取状态对应的CSS类
const getStatusClass = (status) => {
  switch (status) {
    case '待处理': return 'pending';
    case '开发中': return 'developing';
    case '测试中': return 'testing';
    case '验证中': return 'validating';
    case '已完成': return 'completed';
    case '已拒绝': return 'rejected';
    default: return '';
  }
};

// 获取状态对应的徽章CSS类
const getStatusBadgeClass = (status) => {
  switch (status) {
    case '待处理': return 'badge-gray';
    case '开发中': return 'badge-blue';
    case '测试中': return 'badge-orange';
    case '验证中': return 'badge-blue';
    case '已完成': return 'badge-green';
    case '已拒绝': return 'badge-red';
    default: return 'badge-gray';
  }
};

// 获取类型对应的徽章CSS类
const getTypeBadgeClass = (type) => {
  switch (type) {
    case 'Bug Fixed': return 'badge-orange';
    case 'Hot Bug Fixed': return 'badge-red';
    case 'New Feature': return 'badge-blue';
    default: return 'badge-blue';
  }
};

// 获取优先级对应的CSS类
const getPriorityClass = (priority) => {
  if (priority === 'P0' || priority === 'P1') {
    return 'status-high';
  }
  return '';
};

// 获取历史记录项的类型
const getHistoryItemType = (action) => {
  switch (action) {
    case '提交需求': return 'primary';
    case '认领需求': return 'success';
    case '提交测试': return 'warning';
    case '撤回测试': return 'info';
    case '通过测试': return 'success';
    case '驳回测试': return 'danger';
    case '撤回验证': return 'info';
    default: return 'info';
  }
};

// 渲染Markdown内容
const renderMarkdown = (content) => {
  if (!content) return '';
  return marked(content);
};

// 复制到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板');
  }).catch(() => {
    ElMessage.error('复制失败');
  });
};
</script>

<style scoped>
/* 对话框样式调整 */
:deep(.requirement-dialog) {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
}

/* 对话框重置 */
:deep(.el-dialog__body) {
  padding: 24px;
  background-color: #f5f7fa;
}

:deep(.el-dialog__header) {
  display: none;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #e5e6eb;
}

.requirement-detail {
  padding: 0;
  font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif;
  color: #222;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  padding: 24px;
  margin-bottom: 24px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #222;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e6eb;
}

/* 需求标题样式 */
.requirement-header {
  background: linear-gradient(135deg, #1877f2, #36cfc9);
  color: white;
  margin: -24px -24px 24px -24px;
  padding: 24px;
  border-radius: 10px 10px 0 0;
}

.requirement-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.requirement-id {
  font-size: 14px;
  opacity: 0.9;
}

/* 信息网格布局 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 15px;
  color: #222;
  font-weight: 500;
}

.status-high {
  color: #ff4d4f;
  font-weight: bold;
}

/* 人员列表样式 */
.people-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.person-tag {
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 13px;
  color: #666;
}

/* 标签样式 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
}

.badge-blue {
  background-color: rgba(24, 119, 242, 0.1);
  color: #1877f2;
}

.badge-green {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.badge-orange {
  background-color: rgba(245, 166, 35, 0.1);
  color: #f5a623;
}

.badge-red {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.badge-gray {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

/* 内容区域 */
.content-section {
  margin-bottom: 24px;
}

.content-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.content-alert {
  padding: 0;
}

/* 历史记录时间轴样式 */
.timeline {
  position: relative;
  padding-left: 24px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e5e6eb;
}

.timeline-item {
  position: relative;
  margin-bottom: 16px;
  padding-bottom: 16px;
}

.timeline-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #52c41a;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #52c41a;
}

.timeline-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 8px;
}

.timeline-user {
  font-weight: 500;
  color: #222;
}

.timeline-action {
  font-size: 14px;
  color: #666;
}

.timeline-time {
  margin-left: auto;
  font-size: 13px;
  color: #999;
}

.history-remark {
  font-style: italic;
  color: #666;
  font-size: 13px;
  margin-top: 5px;
}

/* 复制按钮 */
.copy-btn {
  margin-left: 8px;
  padding: 4px 8px;
  height: auto;
}

/* 对话框加载状态 */
.dialog-loading {
  padding: 20px;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.close-btn {
  padding: 8px 20px;
}
</style>
